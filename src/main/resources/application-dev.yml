server:
  port: 8239
  servlet:
    context-path: /micro-platform

spring:
  application:
    name: micro-platform
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************
    username: root
    password: Ch3LWChQb59Ya7bpUkgE
    hikari:
      pool-name: Micro-Platform-HikariCP
      connection-timeout: 30000
      maximum-pool-size: 20
      idle-timeout: 600000
      max-lifetime: 150000
      keepaliveTime: 60000
      connection-test-query: SELECT 1
  # 设置tomcat上传文件大小
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB


# redis
spring.data:
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 16089
    # 数据库索引
    database: 0
    # 密码(如没有密码请注释掉)
    password: ViPuz9RBLqVK8Y9oy
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# mybatis-plus 配置
mybatis-plus:
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.weicai.microplatform.entity

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: micro-platform-token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000  # 30天
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true

# FolkMQ
folkmq:
  server: folkmq://************:18602
  consumerGroup: micro-platform-mq

# 对象存储配置
dromara:
  x-file-storage:
    default-platform: qiniu-kodo
    thumbnail-suffix: .jpg
    qiniu-kodo:
      - platform: qiniu-kodo # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: Nl-tR1mysemRinyU_pZITbZYp1dulzZRwghb_5-6
        secret-key: YHYQwYL2uHabKLtGYmO8MPx9MEVCHd_J09L5OND1
        bucket-name: micro-platform
        domain: https://mp-img.weicaiyunxia.com/ # 访问域名 todo
        base-path: image/ # 基础路径

# sms4j 短信配置
sms:
  # 标注从yml读取配置
  config-type: yaml
  blends:
    # 自定义的标识，也就是configId这里可以是任意值（最好不要是中文）
    micro-platform:
      #厂商标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分
      supplier: alibaba
      #您的accessKey
      access-key-id: LTAI5tRy9bV4SRrvjpRF9CLm
      #您的accessKeySecret
      access-key-secret: ******************************
      #您的短信签名
      signature: 云虾软件
      #模板ID 非必须配置，如果使用sendMessage的快速发送需此配置
      template-id: SMS_173695706
      #您的sdkAppId
      sdk-app-id: micro-platform-sms

# xxl-job
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8080/xxl-job-admin
    executor:
      appname: xxl-job-demo
      port: 9999
      accessToken:
