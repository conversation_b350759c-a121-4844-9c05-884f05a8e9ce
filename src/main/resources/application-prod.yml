server:
  port: 8239
  servlet:
    context-path: /micro-platform

spring:
  application:
    name: micro-platform
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************
    username: root
    password: GkN34GbGgzvNpsy4JKRL
    hikari:
      pool-name: Micro-Platform-HikariCP
      connection-timeout: 30000
      maximum-pool-size: 20
      idle-timeout: 600000
      max-lifetime: 150000
      keepaliveTime: 60000
      connection-test-query: SELECT 1

# redis
spring.data:
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 16832
    # 数据库索引
    database: 0
    # 密码(如没有密码请注释掉)
    password: dnH8h7WMPEs9issdmcVX
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# mybatis-plus 配置
mybatis-plus:
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.weicai.microplatform.entity

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: micro-platform-token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true

# FolkMQ
folkmq:
  server: folkmq://127.0.0.1:18602
  consumerGroup: micro-platform-mq
