package com.weicai.microplatform.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.weicai.microplatform.entity.ApiResult;
import com.weicai.microplatform.service.CommonService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 公共接口-控制层
 *
 * <AUTHOR>
 * @date 2024/6/28
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@SaIgnore
@RestController
@RequestMapping("/common")
public class CommonController {

    private final CommonService commonService;

    /**
     * 图片上传
     *
     * @param file 图片文件
     * @return ApiResult
     */
    @PostMapping({"/uploadImage"})
    public ApiResult<Object> imageUpload(@Valid @NotNull(message = "文件不能为空") @RequestParam("file") MultipartFile file) {
        return commonService.uploadImage(file);
    }
}
