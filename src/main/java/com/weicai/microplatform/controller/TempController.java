package com.weicai.microplatform.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.weicai.microplatform.entity.ApiResult;
import com.weicai.microplatform.util.CaffeineCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 测试使用 - 忽略
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/temp")
@RequiredArgsConstructor
public class TempController {

    private final CaffeineCache caffeineCache;

    /**
     * 测试远程调用
     *
     * @return ApiResult
     */
    @PostMapping("/test")
    public ApiResult<Object> test() {
        ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();
        executorService.execute(() -> {
            System.out.println("异步调用成功" + Thread.currentThread());
        });
        // log.info("调用成功, user 参数： {} , 当前线程： {}", user , Thread.currentThread().getName());
        System.out.println("调用成功 console " + Thread.currentThread().getName());
        executorService.shutdown();
        return ApiResult.ok("调用成功");
    }

    /**
     * 测试缓存
     *
     * @return ApiResult
     */
    @PostMapping("/testCaffeine")
    public ApiResult<Object> testCaffeine() {
        caffeineCache.put("key", "调用成功!!!");
        caffeineCache.put("key2", "123成功!!!");
        System.out.println(caffeineCache.get("key"));
        System.out.println(caffeineCache.get("key"));
        System.out.println(caffeineCache.get("key2"));
        return ApiResult.ok("调用成功");
    }

    /**
     * 测试短信发送
     *
     * @return ApiResult
     */
    @PostMapping("/testSms")
    public ApiResult<Object> testSms() {
        System.out.println("开始发送短信" + Thread.currentThread().getName());
        try {
            LinkedHashMap<String, String> params = new LinkedHashMap<>();
            params.put("code", "123456");
            SmsBlend smsBlend = SmsFactory.getSmsBlend("micro-platform");
            // SmsResponse smsResponse = smsBlend.sendMessage("18675877807", params);

            // 异步发送短信
            smsBlend.sendMessageAsync("18675877807", "SMS_173695706" , params, e -> {
                System.out.println("异步发送短信成功" + Thread.currentThread().getName());
            });
            // System.out.println("smsResponse = " + Thread.currentThread().getName() + JSONObject.toJSONString(smsResponse));
        } catch (Exception e) {
            System.err.println("发送短信失败, e = " + e);
        }
        return ApiResult.ok("发送短信成功");
    }

}
