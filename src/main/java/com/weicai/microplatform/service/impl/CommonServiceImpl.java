package com.weicai.microplatform.service.impl;

import com.weicai.microplatform.entity.ApiResult;
import com.weicai.microplatform.service.CommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 公共接口-服务实现
 *
 * <AUTHOR>
 * @date 2024/6/28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CommonServiceImpl implements CommonService {

    private final FileStorageService fileStorageService;

    /**
     * 上传图片
     *
     * @param file 图片文件
     * @return 返回结果
     */
    @Override
    public ApiResult<Object> uploadImage(MultipartFile file) {
        if (file.getSize() > 10 * 1024 * 1024) {
            return ApiResult.fail(400, "文件大小不能超过10M！");
        }

        try {
            String fileName = file.getOriginalFilename();
            assert fileName != null;
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            if (!".jpg,.jpeg,.png,.mp4,.mp3,.wav,.avi".contains(suffixName.toLowerCase())) {
                return ApiResult.fail(400, "不支持的文件类型,请上传图片。");
            }

            // 上传到七牛云
            FileInfo fileInfo = fileStorageService.of(file)
                    // .setThumbnailSuffix(".jpg")
                    // .setSaveFilename("123.jpg")
                    // .image(img -> img.size(750, 750))
                    // .thumbnail(th -> th.scale(1.0))
                    .thumbnail(th -> th.scale(1.0).outputQuality(0.8))
                    .upload();
            if (ObjectUtils.isEmpty(fileInfo) || StringUtils.isBlank(fileInfo.getUrl())) {
                log.error("上传七牛云返回异常：fileInfo 信息为null {}", fileInfo);
                return ApiResult.fail("上传失败，请联系管理员！");
            }

            return ApiResult.ok("上传成功", fileInfo.getUrl());
        } catch (Exception e) {
            log.error("上传图片发生异常：", e);
            return ApiResult.fail("上传失败，请联系管理员！");
        }
    }
}
