package com.weicai.microplatform.aop;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 注解切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class LoggingAspect {

    @Around("@annotation(com.weicai.microplatform.aop.Log)")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求参数
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        Map<String, Object> requestParams = getRequestParams(joinPoint);

        log.info("请求路径: {}, 请求方法: {}, 请求参数: {}", request.getRequestURI(), request.getMethod(), requestParams);

        // 执行目标方法
        Object result = joinPoint.proceed();

        // 记录响应参数
        log.info("响应参数: {}", result);

        return result;
    }

    private Map<String, Object> getRequestParams(JoinPoint joinPoint) {
        Map<String, Object> requestParams = new HashMap<>();

        // 获取参数名
        String[] paramNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        // 获取参数值
        Object[] paramValues = joinPoint.getArgs();

        for (int i = 0; i < paramNames.length; i++) {
            requestParams.put(paramNames[i], paramValues[i]);
        }

        return requestParams;
    }
}
