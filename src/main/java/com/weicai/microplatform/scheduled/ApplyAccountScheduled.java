package com.weicai.microplatform.scheduled;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 申请账户 定时任务
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApplyAccountScheduled {

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static final int INTERVAL_MINUTES = 60;
    private static final int MAX_EXECUTIONS = 2;

    /**
     * 每天22:00执行-数字人制作
     */
    @Scheduled(cron = "0 0 22 * * ?")
    public void scheduleApplyDigitalManTask() {
        log.info("定时任务-开始！！！");
        scheduleNextExecution(0); // 从第一次执行开始
    }

    private void scheduleNextExecution(int executionCount) {
        if (executionCount < MAX_EXECUTIONS) {
            scheduler.schedule(() -> {
                try {
                    log.info("开始执行数字人制作申请！！！");
                    // digitalManService.applyDigitalMan();
                } catch (Exception e) {
                    log.error("数字人制作申请失败：", e);
                }
                scheduleNextExecution(executionCount + 1); // 递归调用，执行下一次任务
            }, INTERVAL_MINUTES * 60, TimeUnit.SECONDS); // 每次间隔60分钟
        } else {
            log.info("任务完成，调度器已停止。");
        }
    }
}
