package com.weicai.microplatform.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申请状态
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
@Getter
@AllArgsConstructor
public enum ApplyStatus {

    NO("0", "未开通"),

    APPROVING("1", "审批中"),

    APPLYING("2", "开通中"),

    SUCCESS("3", "已开通"),

    FAILED("4", "开通失败"),

    REJECTED("5", "审批拒绝");

    private final String code;
    private final String info;
}
