package com.weicai.microplatform.config;

import lombok.extern.slf4j.Slf4j;
import org.noear.folkmq.FolkMQ;
import org.noear.folkmq.client.MqClient;
import org.noear.folkmq.client.MqConsumeHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Slf4j
@Configuration
public class FolkMQConfig {
    @Bean
    public MqClient initClient(@Value("${folkmq.server}") String serverUrl,
                               @Value("${folkmq.consumerGroup}") String consumerGroup,
                               @Autowired Map<String, MqConsumeHandler> subscriptionMap) throws Exception {
        //构建客户端
        MqClient client = FolkMQ.createClient(serverUrl)
                .nameAs(consumerGroup)
                .connect();

        //订阅
        for (Map.Entry<String, MqConsumeHandler> subscription : subscriptionMap.entrySet()) {
            client.subscribe(subscription.getKey(), subscription.getValue());
        }

        log.info("micro-platform folkmq client 初始化成功。。。");
        return client;
    }
}
