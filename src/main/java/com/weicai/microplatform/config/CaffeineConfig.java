package com.weicai.microplatform.config;

import com.github.benmanes.caffeine.cache.Caffeine;

import org.springframework.context.annotation.Bean;

import org.springframework.context.annotation.Configuration;


import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Configuration
public class CaffeineConfig {

    @Bean(name = "defaultCaffeineConfig")
    public Caffeine<Object, Object> caffeineConfig() {
        return Caffeine.newBuilder()
                .initialCapacity(10)
                .maximumSize(500)
                .expireAfterWrite(120, TimeUnit.MINUTES)
                // .expireAfterAccess(120, TimeUnit.MINUTES)
                .weakKeys()
                .recordStats();
    }

}
