package com.weicai.microplatform.entity;

import com.weicai.microplatform.constant.HttpStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 响应信息主体
 *
 * <AUTHOR> Li
 */
@Data
@NoArgsConstructor
public class ApiResult<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = -5202477153696301578L;

    /**
     * 成功
     */
    public static final int SUCCESS = 200;

    /**
     * 失败
     */
    public static final int FAIL = 500;

    private int code;

    private String msg;

    private T data;

    public static <T> ApiResult<T> ok() {
        return restApiResultesult(null, SUCCESS, "操作成功");
    }

    public static <T> ApiResult<T> ok(T data) {
        return restApiResultesult(data, SUCCESS, "操作成功");
    }

    public static <T> ApiResult<T> ok(String msg) {
        return restApiResultesult(null, SUCCESS, msg);
    }

    public static <T> ApiResult<T> ok(String msg, T data) {
        return restApiResultesult(data, SUCCESS, msg);
    }

    public static <T> ApiResult<T> fail() {
        return restApiResultesult(null, FAIL, "操作失败");
    }

    public static <T> ApiResult<T> fail(String msg) {
        return restApiResultesult(null, FAIL, msg);
    }

    public static <T> ApiResult<T> fail(T data) {
        return restApiResultesult(data, FAIL, "操作失败");
    }

    public static <T> ApiResult<T> fail(String msg, T data) {
        return restApiResultesult(data, FAIL, msg);
    }

    public static <T> ApiResult<T> fail(int code, String msg) {
        return restApiResultesult(null, code, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static <T> ApiResult<T> warn(String msg) {
        return restApiResultesult(null, HttpStatus.WARN, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <T> ApiResult<T> warn(String msg, T data) {
        return restApiResultesult(data, HttpStatus.WARN, msg);
    }

    private static <T> ApiResult<T> restApiResultesult(T data, int code, String msg) {
        ApiResult<T> r = new ApiResult<>();
        r.setCode(code);
        r.setData(data);
        r.setMsg(msg);
        return r;
    }

    public static <T> Boolean isError(ApiResult<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(ApiResult<T> ret) {
        return ApiResult.SUCCESS == ret.getCode();
    }
}
