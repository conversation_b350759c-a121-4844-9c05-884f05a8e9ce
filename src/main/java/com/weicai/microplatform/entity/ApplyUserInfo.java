package com.weicai.microplatform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 申请用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@TableName("apply_user_info")
public class ApplyUserInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 申请者姓名
     */
    @NotBlank(message = "姓名不能为空")
    @TableField("username")
    private String username;

    /**
     * 微信昵称
     */
    @TableField("wx_nickname")
    private String wxNickname;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @TableField("phone")
    private String phone;

    /**
     * 性别：0-女 1-男 2-未知
     */
    @TableField("sex")
    private String sex;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 公司
     */
    @NotBlank(message = "公司名不能为空")
    @TableField("company")
    private String company;

    /**
     * 公司所属行业
     */
    @TableField("company_industry")
    private String companyIndustry;

    /**
     * 公司规模
     */
    @TableField("company_size")
    private String companySize;

    /**
     * 公司营业额
     */
    @TableField("company_revenue")
    private String companyRevenue;

    /**
     * 公司地址
     */
    @TableField("company_address")
    private String companyAddress;

    /**
     * 公司预算
     */
    @TableField("company_budget")
    private String companyBudget;

    /**
     * 企业需求
     */
    @TableField("company_need")
    private String companyNeed;

    /**
     * 申请功能：beauty-美业 retail-零售 名片-businesscard mes-生产管理系统
     * erp-企业资源管理 oa-企业办公 website-官网 youxuan-唯裁优选
     */
    @NotBlank(message = "申请功能不能为空")
    @TableField("apply_account")
    private String applyAccount;

    /**
     * 已开通账户
     */
    @TableField("open_account")
    private String openAccount;

    /**
     * 推荐人
     */
    @TableField("recommender")
    private String recommender;

    /**
     * 推荐人手机
     */
    @TableField("recommender_phone")
    private String recommenderPhone;

    /**
     * 微信openid
     */
    @TableField("openid")
    private String openid;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 职位
     */
    @TableField("position")
    private String position;
}
