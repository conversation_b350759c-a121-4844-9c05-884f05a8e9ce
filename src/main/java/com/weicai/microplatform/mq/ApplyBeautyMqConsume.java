package com.weicai.microplatform.mq;

import lombok.extern.slf4j.Slf4j;
import org.noear.folkmq.client.MqConsumeHandler;
import org.noear.folkmq.client.MqMessageReceived;
import org.springframework.stereotype.Component;

/**
 * 申请美业账号MQ消费
 *
 * <AUTHOR>
 */
@Component("apply.account.beauty")
@Slf4j
public class ApplyBeautyMqConsume implements MqConsumeHandler {

    // @Resource
    // private BeautyService beautyService;

    @Override
    public void consume(MqMessageReceived message) {
        String msgContent = message.getBodyAsString();
        log.info("收到申请美业账号MQ消息：{}", msgContent);

        try {
            // ApplyUserInfo userInfo = JSON.parseObject(msgContent, ApplyUserInfo.class);
            // beautyService.requestBeautyAccount(userInfo);
        } catch (Exception e) {
            log.error("申请美业账号MQ消息处理失败：", e);
        }
    }

}
