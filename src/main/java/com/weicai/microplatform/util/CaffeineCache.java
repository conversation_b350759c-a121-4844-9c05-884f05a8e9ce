package com.weicai.microplatform.util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class CaffeineCache {

    private final Caffeine<Object, Object> caffeine;

    private Cache<String, Object> cache;

    @Autowired
    public CaffeineCache(@Qualifier("defaultCaffeineConfig")Caffeine<Object, Object> caffeine) {
        this.caffeine = caffeine;
    }

    @PostConstruct
    public void init() {
        this.cache = caffeine.build();
    }

    /**
     * 获取缓存中的值
     *
     * @param key 缓存键
     * @return 缓存值
     */
    public Object get(String key) {
        return cache.getIfPresent(key);
    }

    /**
     * 放置值到缓存
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    public void put(String key, Object value) {
        cache.put(key, value);
    }

    /**
     * 从缓存中移除值
     *
     * @param key 缓存键
     */
    public void remove(String key) {
        cache.invalidate(key);
    }

    /**
     * 清空缓存
     */
    public void clear() {
        cache.invalidateAll();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String stats() {
        return cache.stats().toString();
    }

}
