<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro 80s Terminal</title>
    <link rel="stylesheet" href="vintage_theme_1.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .crt-effect {
            animation: flicker 0.15s infinite;
            position: relative;
        }
        @keyframes flicker {
            0% { opacity:0.9; }
            50% { opacity:1; }
            100% { opacity:0.9; }
        }
    </style>
</head>
<body class="crt-effect font-mono p-4 h-screen flex flex-col bg-[var(--background)] text-[var(--foreground)]">
    <div class="flex-1 overflow-auto border-2 border-[var(--border)] p-4 mb-4 shadow-lg">
        <div class="text-green-400">
            <p>> SYSTEM INITIALIZED █</p>
            <p class="mt-4">1984-01-04 08:23:17</p>
            <p class="mt-2">> WELCOME TO TERMINAL 01</p>
        </div>
    </div>
    <div class="flex gap-2">
        <input type="text" 
               class="flex-1 bg-[var(--input)] border-2 border-[var(--border)] p-2 text-sm focus:ring-[var(--ring)]"
               placeholder="ENTER COMMAND...">
        <button class="px-4 py-2 bg-[var(--primary)] text-[var(--primary-foreground)] hover:brightness-110 transition">
            EXEC
        </button>
    </div>
</body>
</html>